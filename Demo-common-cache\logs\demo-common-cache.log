2025-07-26 15:20:21.190 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-26 15:20:21.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-26 15:20:21.196 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-26 15:20:21.200 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-26 15:20:21.200 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-26 15:20:21.213 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-26 15:20:21.214 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=22ms
2025-07-26 15:25:21.221 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-26 15:25:21.227 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-26 15:25:21.228 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-26 15:25:21.231 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-26 15:25:21.231 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-26 15:25:21.237 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-26 15:25:21.237 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=16ms
2025-07-26 15:30:21.243 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-26 15:30:21.246 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-26 15:30:21.247 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-26 15:30:21.251 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-26 15:30:21.252 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-26 15:30:21.254 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-26 15:30:21.256 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=12ms
2025-07-26 15:35:21.272 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-26 15:35:21.277 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-26 15:35:21.277 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-26 15:35:21.280 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-26 15:35:21.282 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-26 15:35:21.284 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-26 15:35:21.284 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=12ms
2025-07-26 15:40:21.288 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Starting scheduled cleanup...
2025-07-26 15:40:21.293 [scheduling-1] DEBUG c.n.common.cache.service.impl.SessionManagerImpl - Cleaned up 0 expired sessions
2025-07-26 15:40:21.294 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired sessions
2025-07-26 15:40:21.298 [scheduling-1] DEBUG c.n.common.cache.service.impl.CacheManagerImpl - Cleaned up 0 expired caches
2025-07-26 15:40:21.298 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired caches
2025-07-26 15:40:21.302 [scheduling-1] DEBUG c.n.common.cache.service.impl.RedisCleanerImpl - Cleaned 0 expired locks
2025-07-26 15:40:21.302 [scheduling-1] INFO  c.n.common.cache.service.impl.RedisCleanerImpl - Scheduled cleanup completed: sessions=0, caches=0, locks=0, duration=13ms
2025-07-26 15:44:08.632 [redisson-timer-4-1] DEBUG org.redisson.client.handler.ConnectionWatchdog - reconnecting RedisConnection@6837202 [redisClient=[addr=redis://localhost:6379], channel=[id: 0x48207757, L:/127.0.0.1:4475 ! R:localhost/127.0.0.1:6379], currentCommand=null, usage=0] to localhost/127.0.0.1:6379 
2025-07-26 15:44:08.646 [redisson-netty-2-3] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-26 15:44:08.647 [redisson-timer-4-1] DEBUG org.redisson.client.handler.ConnectionWatchdog - reconnecting RedisPubSubConnection@378240939 [redisClient=[addr=redis://localhost:6379], channel=[id: 0x8c8958d1, L:/127.0.0.1:4472 ! R:localhost/127.0.0.1:6379], currentCommand=null, usage=0] to localhost/127.0.0.1:6379 
2025-07-26 15:44:08.649 [redisson-timer-4-1] DEBUG org.redisson.client.handler.ConnectionWatchdog - reconnecting RedisConnection@768949980 [redisClient=[addr=redis://localhost:6379], channel=[id: 0x6c48c926, L:/127.0.0.1:4474 ! R:localhost/127.0.0.1:6379], currentCommand=null, usage=0] to localhost/127.0.0.1:6379 
2025-07-26 15:44:08.649 [redisson-netty-2-4] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-26 15:44:08.651 [redisson-netty-2-5] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-26 15:44:08.726 [redisson-timer-4-1] DEBUG org.redisson.client.handler.ConnectionWatchdog - reconnecting RedisConnection@6837202 [redisClient=[addr=redis://localhost:6379], channel=[id: 0x48207757, L:/127.0.0.1:4475 ! R:localhost/127.0.0.1:6379], currentCommand=null, usage=0] to localhost/127.0.0.1:6379 
2025-07-26 15:44:08.727 [redisson-timer-4-1] DEBUG org.redisson.client.handler.ConnectionWatchdog - reconnecting RedisConnection@768949980 [redisClient=[addr=redis://localhost:6379], channel=[id: 0x6c48c926, L:/127.0.0.1:4474 ! R:localhost/127.0.0.1:6379], currentCommand=null, usage=0] to localhost/127.0.0.1:6379 
2025-07-26 15:44:08.728 [redisson-timer-4-1] DEBUG org.redisson.client.handler.ConnectionWatchdog - reconnecting RedisPubSubConnection@378240939 [redisClient=[addr=redis://localhost:6379], channel=[id: 0x8c8958d1, L:/127.0.0.1:4472 ! R:localhost/127.0.0.1:6379], currentCommand=null, usage=0] to localhost/127.0.0.1:6379 
2025-07-26 15:44:08.728 [redisson-netty-2-6] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-26 15:44:08.728 [redisson-netty-2-7] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
2025-07-26 15:44:08.729 [redisson-netty-2-8] DEBUG org.redisson.client.RedisConnection - Connection created [addr=redis://localhost:6379]
